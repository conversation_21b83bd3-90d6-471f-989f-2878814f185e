/**
 *
 * @param {Array} data
 * @param {Boolean} isIos
 *
 */
export function progressCareCalendarCalculate(data, isIos) {
  const result = [];
  // Get today's date and set its time to 00:00:00 for accurate comparison
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  for (let i = 0, len = data.length; i < len; i++) {
    const { date, urine, water, weight, bloodPressure, step, woman } = data[i];

    // Create a Date object from the current data's date string
    const dataDate = new Date(date);
    dataDate.setHours(0, 0, 0, 0); // Normalize time to 00:00:00

    // Check if the current date is before today
    if (dataDate > today) {
      // If the date is in the past, stop the loop.
      // This will break the loop at the first occurrence of a past date.
      break;
    }

    let p = 0;
    if (urine && urine.length !== 0) p++;
    if (water && water.length !== 0) p++;
    if (weight && weight.length !== 0) p++;
    if (bloodPressure && bloodPressure.length !== 0) p++;
    if (!isIos) {
      // 체 수 배 혈 걸
      if (step && step !== null) p++;
    }
    const total = isIos ? 4 : 5;

    const progress = Math.round((p / total) * 100);

    result.push({
      date,
      progress,
    });
  }

  return result;
}
/**
 * @param {string} date
 * @param {Array} data
 */
export function detailCareCalendarData(date, data) {
  for (const dateData of data) {
    if (date === dateData.date) {
      const { progress, ...detail } = dateData;
      return { ...detail };
    }
  }
}
