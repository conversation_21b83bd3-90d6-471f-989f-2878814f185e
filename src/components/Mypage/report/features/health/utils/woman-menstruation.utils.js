/**
 * {
  "fertileWindow": {
    "startDate": "2025-09-13",
    "endDate": "2025-09-19"
  },
  "ovulationDay": "2025-09-17",
  "nextMenstruation": {
    "startDate": "2025-10-01",
    "endDate": "2025-10-05"
  },
  "result": [
    {
      "id": 163,
      "subjectId": 1416,
      "startDate": "2025-09-01",
      "endDate": "2025-09-05"
    },
    {
      "id": 138,
      "subjectId": 1416,
      "startDate": "2025-05-14",
      "endDate": "2025-05-18"
    },
    {
      "id": 44,
      "subjectId": 1416,
      "startDate": "2024-09-01",
      "endDate": "2024-09-05"
    }
  ]
}
 */

export function getMenstruationCycle(menstruationData) {
  let cycle = 0;

  if (
    menstruationData &&
    menstruationData.result &&
    menstruationData.result.length > 0 &&
    menstruationData.nextMenstruation
  ) {
    const sortedResult = menstruationData.result.sort(
      (a, b) => new Date(b.startDate) - new Date(a.startDate)
    );
    const latestMenstruation = sortedResult[0];

    const latestStartDate = new Date(latestMenstruation.startDate);
    const nextStartDate = new Date(menstruationData.nextMenstruation.startDate);

    // 날짜 간 차이를 계산 (밀리초 단위)
    const diffInMilliseconds =
      nextStartDate.getTime() - latestStartDate.getTime();

    // 밀리초를 일(day)로 변환
    const diffInDays = Math.floor(diffInMilliseconds / (1000 * 60 * 60 * 24));

    cycle = diffInDays;
  }

  return cycle;
}

export function formatMenstruationData(menstruationData) {
  const hasRecord =
    menstruationData &&
    menstruationData.result &&
    menstruationData.result.length > 0;

  // 가장 최근 생리 데이터 가져오기
  let latestMenstruation = null;
  if (hasRecord) {
    const sortedResult = menstruationData.result.sort(
      (a, b) => new Date(b.startDate) - new Date(a.startDate)
    );
    latestMenstruation = sortedResult[0];
  }

  const avoidUrineTestDate = latestMenstruation?.startDate ?? null;
  const menstruationPeriod = latestMenstruation
    ? `${latestMenstruation.startDate} ~ ${latestMenstruation.endDate}`
    : null;

  const menstruationCycle = getMenstruationCycle(menstruationData);

  return {
    hasRecord,
    avoidUrineTestDate,
    menstruationPeriod,
    menstruationCycle,
    latestMenstruation,
    nextMenstruation: menstruationData.nextMenstruation,
    ovulationDay: menstruationData.ovulationDay,
    fertileWindow: menstruationData.fertileWindow,
  };
}
