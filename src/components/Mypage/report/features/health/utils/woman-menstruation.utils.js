/**
 * 생리 데이터 구조 예시
 * {
  "fertileWindow": {
    "startDate": "2025-09-13",
    "endDate": "2025-09-19"
  },
  "ovulationDay": "2025-09-17",
  "nextMenstruation": {
    "startDate": "2025-10-01",
    "endDate": "2025-10-05"
  },
  "result": [
    {
      "id": 163,
      "subjectId": 1416,
      "startDate": "2025-09-01",
      "endDate": "2025-09-05"
    },
    {
      "id": 138,
      "subjectId": 1416,
      "startDate": "2025-05-14",
      "endDate": "2025-05-18"
    },
    {
      "id": 44,
      "subjectId": 1416,
      "startDate": "2024-09-01",
      "endDate": "2024-09-05"
    }
  ]
}
 */

/**
 * 시스템 언어에 따라 날짜를 포맷팅합니다.
 * @param {string} dateString - YYYY-MM-DD 형식의 날짜 문자열
 * @returns {string} 포맷팅된 날짜 문자열
 */
function formatDateByLocale(dateString) {
  if (!dateString) return "";

  const date = new Date(dateString);
  const locale = navigator.language || navigator.userLanguage || "en";

  if (locale.startsWith("ko")) {
    // 한국어: "2월 18일" 형식
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}월 ${day}일`;
  } else {
    // 영어 및 기타: "02.18" 형식
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${month}.${day}`;
  }
}

/**
 * 생리 주기를 계산합니다.
 * @param {Object} menstruationData - 생리 데이터 객체
 * @returns {number} 생리 주기 (일 단위)
 */

export function getMenstruationCycle(menstruationData) {
  let cycle = 0;

  if (
    menstruationData &&
    menstruationData.result &&
    menstruationData.result.length > 0 &&
    menstruationData.nextMenstruation
  ) {
    // 생리 기록을 최신순으로 정렬
    const sortedResult = menstruationData.result.sort(
      (a, b) => new Date(b.startDate) - new Date(a.startDate)
    );
    const latestMenstruation = sortedResult[0];

    const latestStartDate = new Date(latestMenstruation.startDate);
    const nextStartDate = new Date(menstruationData.nextMenstruation.startDate);

    // 날짜 간 차이를 계산 (밀리초 단위)
    const diffInMilliseconds =
      nextStartDate.getTime() - latestStartDate.getTime();

    // 밀리초를 일(day)로 변환
    const diffInDays = Math.floor(diffInMilliseconds / (1000 * 60 * 60 * 24));

    cycle = diffInDays;
  }

  return cycle;
}

/**
 * 생리 데이터를 포맷팅하여 UI에서 사용할 수 있는 형태로 변환합니다.
 * @param {Object} menstruationData - 생리 데이터 객체
 * @returns {Object} 포맷팅된 생리 데이터
 */
export function formatMenstruationData(menstruationData) {
  // 생리 기록이 있는지 확인
  const hasRecord =
    menstruationData &&
    menstruationData.result &&
    menstruationData.result.length > 0;

  // 가장 최근 생리 데이터 가져오기
  let latestMenstruation = null;
  if (hasRecord) {
    const sortedResult = menstruationData.result.sort(
      (a, b) => new Date(b.startDate) - new Date(a.startDate)
    );
    latestMenstruation = sortedResult[0];
  }

  // 소변검사 피해야 할 날짜 (가장 최근 생리 시작일)
  const avoidUrineTestDate = latestMenstruation?.startDate ?? null;

  // 생리 기간을 시스템 언어에 맞게 포맷팅
  const menstruationPeriod = latestMenstruation
    ? `${formatDateByLocale(
        latestMenstruation.startDate
      )} ~ ${formatDateByLocale(latestMenstruation.endDate)}`
    : null;

  // 생리 주기 계산
  const menstruationCycle = getMenstruationCycle(menstruationData);

  return {
    hasRecord, // 생리 기록 존재 여부
    avoidUrineTestDate, // 소변검사 피해야 할 날짜
    menstruationPeriod, // 포맷팅된 생리 기간
    menstruationCycle, // 생리 주기 (일 단위)
    latestMenstruation, // 가장 최근 생리 데이터
    nextMenstruation: menstruationData.nextMenstruation, // 다음 생리 예정일
    ovulationDay: menstruationData.ovulationDay, // 배란일
    fertileWindow: menstruationData.fertileWindow, // 가임기
  };
}
