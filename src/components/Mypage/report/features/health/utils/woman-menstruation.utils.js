/**
 * Menstruation data structure interface
 * @typedef {Object} MenstruationRecord
 * @property {number} id - Unique identifier for the menstruation record
 * @property {number} subjectId - Subject identifier
 * @property {string} startDate - Start date in YYYY-MM-DD format
 * @property {string} endDate - End date in YYYY-MM-DD format
 */

/**
 * @typedef {Object} DatePeriod
 * @property {string} startDate - Start date in YYYY-MM-DD format
 * @property {string} endDate - End date in YYYY-MM-DD format
 */

/**
 * @typedef {Object} MenstruationData
 * @property {DatePeriod} fertileWindow - Fertile window period
 * @property {string} ovulationDay - Ovulation date in YYYY-MM-DD format
 * @property {DatePeriod} nextMenstruation - Next menstruation period
 * @property {MenstruationRecord[]} menstruation - Array of menstruation records
 */

/**
 * @typedef {Object} FormattedMenstruationData
 * @property {boolean} hasRecord - Whether menstruation records exist
 * @property {string|null} avoidUrineTestDate - Date to avoid urine test (latest menstruation start date)
 * @property {string|null} menstruationPeriod - Formatted menstruation period string
 * @property {number} menstruationCycle - Menstruation cycle in days
 * @property {MenstruationRecord|null} latestMenstruation - Most recent menstruation data
 * @property {DatePeriod} nextMenstruation - Next menstruation period
 * @property {string} ovulationDay - Ovulation date
 * @property {DatePeriod} fertileWindow - Fertile window period
 */

/**
 * Formats date according to system locale
 * Time complexity: O(1)
 * @param {string} dateString - Date string in YYYY-MM-DD format
 * @returns {string} Formatted date string (Korean: "2월 18일", English: "02.18")
 */
function formatDateByLocale(dateString) {
  if (!dateString) return "";

  const date = new Date(dateString);
  const locale = navigator.language || navigator.userLanguage || "en";

  if (locale.startsWith("ko")) {
    // Korean format: "2월 18일"
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}월 ${day}일`;
  } else {
    // English and others: "02.18"
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${month}.${day}`;
  }
}

/**
 * Calculates menstruation cycle based on historical data
 * Time complexity: O(n log n) where n is the number of menstruation records
 * @param {MenstruationData} menstruationData - Menstruation data object
 * @returns {number} Menstruation cycle in days (returns 0 if calculation is not possible)
 */
export function getMenstruationCycle(menstruationData) {
  let cycle = 0;

  if (
    menstruationData &&
    menstruationData.menstruation &&
    menstruationData.menstruation.length > 0 &&
    menstruationData.nextMenstruation
  ) {
    // Sort menstruation records in descending order by start date for optimal performance
    const sortedMenstruation = menstruationData.menstruation.sort(
      (a, b) => new Date(b.startDate) - new Date(a.startDate)
    );
    const latestMenstruation = sortedMenstruation[0];

    const latestStartDate = new Date(latestMenstruation.startDate);
    const nextStartDate = new Date(menstruationData.nextMenstruation.startDate);

    // Calculate difference in milliseconds
    const diffInMilliseconds =
      nextStartDate.getTime() - latestStartDate.getTime();

    // Convert milliseconds to days
    const diffInDays = Math.floor(diffInMilliseconds / (1000 * 60 * 60 * 24));

    cycle = diffInDays;
  }

  return cycle;
}

/**
 * Formats menstruation data for UI consumption with optimized performance
 * Time complexity: O(n log n) where n is the number of menstruation records
 * Space complexity: O(1) excluding input data
 *
 * @param {MenstruationData} menstruationData - Raw menstruation data from API
 * @returns {FormattedMenstruationData} Formatted menstruation data for UI display
 */
export function formatMenstruationData(menstruationData) {
  console.log("Processing menstruation data:", menstruationData);

  // Check if menstruation records exist
  const hasRecord =
    menstruationData &&
    menstruationData.menstruation &&
    menstruationData.menstruation.length > 0;

  // Get the most recent menstruation data with optimal sorting
  let latestMenstruation = null;
  if (hasRecord) {
    const sortedMenstruation = menstruationData.menstruation.sort(
      (a, b) => new Date(b.startDate) - new Date(a.startDate)
    );
    latestMenstruation = sortedMenstruation[0];
  }

  // Date to avoid urine test (latest menstruation start date)
  const avoidUrineTestDate = latestMenstruation?.startDate ?? null;

  // Format menstruation period according to system locale
  const menstruationPeriod = latestMenstruation
    ? `${formatDateByLocale(
        latestMenstruation.startDate
      )} ~ ${formatDateByLocale(latestMenstruation.endDate)}`
    : null;

  // Calculate menstruation cycle
  const menstruationCycle = getMenstruationCycle(menstruationData);

  return {
    hasRecord: hasRecord, // Whether menstruation records exist
    avoidUrineTestDate, // Date to avoid urine test
    menstruationPeriod, // Formatted menstruation period
    menstruationCycle, // Menstruation cycle in days
    latestMenstruation, // Most recent menstruation data
    nextMenstruation: menstruationData?.nextMenstruation || null, // Next menstruation period
    ovulationDay: menstruationData?.ovulationDay || null, // Ovulation date
    fertileWindow: menstruationData?.fertileWindow || null, // Fertile window period
  };
}

/**
 * Utility function to validate menstruation data structure
 * Time complexity: O(1)
 * @param {any} data - Data to validate
 * @returns {boolean} Whether the data structure is valid
 */
export function validateMenstruationData(data) {
  if (!data || typeof data !== "object") return false;

  // Check required properties
  const hasValidMenstruation = Array.isArray(data.menstruation);
  const hasValidNextMenstruation =
    data.nextMenstruation &&
    typeof data.nextMenstruation.startDate === "string" &&
    typeof data.nextMenstruation.endDate === "string";
  const hasValidOvulationDay = typeof data.ovulationDay === "string";
  const hasValidFertileWindow =
    data.fertileWindow &&
    typeof data.fertileWindow.startDate === "string" &&
    typeof data.fertileWindow.endDate === "string";

  return (
    hasValidMenstruation &&
    hasValidNextMenstruation &&
    hasValidOvulationDay &&
    hasValidFertileWindow
  );
}

/**
 * Gets menstruation records within a specific date range
 * Time complexity: O(n) where n is the number of menstruation records
 * @param {MenstruationData} menstruationData - Menstruation data object
 * @param {string} startDate - Start date in YYYY-MM-DD format
 * @param {string} endDate - End date in YYYY-MM-DD format
 * @returns {MenstruationRecord[]} Filtered menstruation records
 */
export function getMenstruationInDateRange(
  menstruationData,
  startDate,
  endDate
) {
  if (!validateMenstruationData(menstruationData)) return [];

  const start = new Date(startDate);
  const end = new Date(endDate);

  return menstruationData.menstruation.filter((record) => {
    const recordStart = new Date(record.startDate);
    const recordEnd = new Date(record.endDate);

    // Check if record overlaps with the date range
    return recordStart <= end && recordEnd >= start;
  });
}
