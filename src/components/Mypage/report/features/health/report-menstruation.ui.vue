<template>
  <main class="menstruation-section">
    <h1 class="title">우먼</h1>

    <section class="section-card">
      <p class="card-text">{{ getMenstruationCycleText }}</p>
      <ProgressBar
        :currentValue="28"
        :averageValue="getMenstruationCycle"
        unit="일"
        :labelCurrent="`${getMonth}월 주기`"
        labelAverage="평균주기"
      />
    </section>

    <hr class="divider" />

    <section class="section-card">
      <p class="card-text">
        2월 생리 기간은 2월 16일~2월 21일로 총 6일 입니다.
      </p>
      <ProgressBar
        :currentValue="6"
        :averageValue="7"
        unit="일"
        labelCurrent="2월 기간"
        labelAverage="평균기간"
      />
    </section>

    <footer class="info-box">
      <p v-html="getNextMenstruationDate"></p>
    </footer>
  </main>
</template>

<script>
import ProgressBar from "../../ui/woman-menstruation-bar.ui.vue";

import { formatMenstruationData } from "./utils/woman-menstruation.utils";

export default {
  name: "MenstrualCycleTracker",

  components: {
    ProgressBar,
  },

  props: {
    selectedDate: {
      type: String,
      default: () => new Date().toISOString(),
    },

    menstruationData: {
      type: Object,
      default: () => ({
        fertileWindow: {
          startDate: "2025-09-13",
          endDate: "2025-09-19",
        },
        ovulationDay: "2025-09-17",
        nextMenstruation: {
          startDate: "2025-10-01",
          endDate: "2025-10-05",
        },
        menstruation: [
          {
            id: 163,
            subjectId: 1416,
            startDate: "2025-09-01",
            endDate: "2025-09-05",
          },
          {
            id: 138,
            subjectId: 1416,
            startDate: "2025-05-14",
            endDate: "2025-05-18",
          },
          {
            id: 44,
            subjectId: 1416,
            startDate: "2024-09-01",
            endDate: "2024-09-05",
          },
        ],
      }),
    },
  },

  data() {
    return {
      formattedData: {},
      MAX_WEEK: 7,
    };
  },

  methods: {},

  computed: {
    getMenstruationCycleText() {
      const selectedDate = this.selectedDate;
      const [_, month] = selectedDate.split("-");

      if (!this.formattedData.hasRecord) {
        return `${parseInt(month)}월 생리 주기 기록이 없습니다.`;
      }

      const cycle = this.formattedData.menstruationCycle;

      return `${parseInt(month)}월 생리 주기는 ${cycle}일 입니다.`;
    },

    getMenstruationCycle() {
      const cycle = this.formattedData.menstruationCycle;

      return cycle;
    },

    getMonth() {
      const selectedDate = this.selectedDate;
      const [_, month] = selectedDate;

      return parseInt(month);
    },

    getNextMenstruationDate() {
      if (!this.formattedData.hasRecord) {
        return "기록이 없습니다. Cym<sup>702</sup> 앱 내 우먼케어를 활용하여 생리 주기 관리를 해보세요!";
      }

      const avoidDate = this.formattedData.avoidUrineTestDate;
      const [_year, month, day] = avoidDate.split("-");
      const avoidUrineTestDate = `${parseInt(month)}월 ${parseInt(day)}일`;

      return `다음 생리 예상일은 ${avoidUrineTestDate} 입니다. 생리 기간에는 소변검사를 피해주세요.`;
    },
  },

  mounted() {
    const data = this.menstruationData;

    console.log(this.menstruationData);

    this.formattedData = formatMenstruationData(data);

    console.log(this.formattedData);
  },
};
</script>

<style lang="scss" scoped>
.menstruation-section {
  width: 100% !important;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-content: flex-start;

  > section > nav {
    display: flex;
    justify-content: center;
  }
}

.title {
  font-size: 1.5rem; /* Equivalent to text-2xl */
  font-weight: bold;
  margin-bottom: 1.5rem; /* Equivalent to mb-6 */
  text-align: start;
}

.divider {
  margin: 30px 0;
  border: 2px solid #ededed;
  border-radius: 10px;
}

.info-box {
  margin-top: 30px;
  padding: 20px 30px; /* Equivalent to p-4 */
  background-color: #f8f8f8; /* Equivalent to bg-gray-100 */
  border-radius: 10px; /* Equivalent to rounded-lg */
  font-size: 14px; /* Equivalent to text-base */
  line-height: 1.625; /* Equivalent to leading-relaxed */
  color: #000000;
  > p {
    margin: 0;
  }
}

.card-text {
  width: 100%;
  font-size: 14px;
  line-height: 24px;
  letter-spacing: -0.03em;
  color: #000000;
  font-weight: 500;
  text-align: left;
  margin-bottom: 30px;
}
</style>
