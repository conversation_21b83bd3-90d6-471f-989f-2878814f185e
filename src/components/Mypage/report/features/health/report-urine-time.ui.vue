<template>
  <main class="urine-time-section">
    <header class="urine-time-header">
      <h3 class="urine-time-title">배뇨 시간</h3>
      <span>총 {{ totalDaysRecord }}일 기록</span>
    </header>

    <section>
      <article>
        <div class="urine-time-count">
          <p class="urine-time-text">
            한달 평균 하루 배뇨 횟수는 {{ averageUrinesTimes }}회 입니다. 이번달
            전체 배뇨 횟수는 총 {{ totalUrinesTimes }}회 입니다.
          </p>
        </div>
      </article>

      <!-- Canvas 차트 섹션 -->
      <article class="chart-section">
        <div class="chart-container">
          <canvas
            ref="chartCanvas"
            :width="canvasWidth"
            :height="canvasHeight"
            class="bubble-chart"
          ></canvas>
        </div>
      </article>
    </section>
    <footer class="urine-time-footer">
      <div
        class="urine-time-item"
        v-for="(time, idx) in urinesTimes"
        :key="urinesInfo[idx].title"
      >
        <h5 class="urine-time-item-title">
          <div
            class="point-color"
            :style="`background: ${urinesInfo[idx].color}`"
          />
          {{ urinesInfo[idx].title }}
        </h5>
        <span class="item-time">{{ time }}회</span>
        <span class="hours-range">{{ urinesInfo[idx].hoursRange }}h</span>
      </div>
    </footer>
  </main>
</template>

<script>
export default {
  name: "ReportUrineTime",
  props: {
    urinesTimes: {
      type: Array,
      // [morning, lunch, evening]
      default: () => [0, 0, 0],
      required: true,
      validator: (value) => value.length === 3,
    },
    selectedDate: {
      type: String,
      required: true,
      default: () => {
        const date = new Date();
        return `${date.getFullYear()}-${date.getMonth() + 1}`;
      },
    },
  },
  data() {
    return {
      canvasWidth: 240,
      canvasHeight: 130,
      urinesInfo: [
        {
          title: "아침",
          color: "#C9F4F8",
          hoursRange: "01~09",
        },
        {
          title: "점심",
          color: "#FFCC00",
          hoursRange: "09~17",
        },
        {
          title: "저녁",
          color: "#A7A7A7",
          hoursRange: "17~01",
        },
      ],
    };
  },
  computed: {
    totalDaysRecord() {
      if (this.urinesTimes.length === 0) return 0;
      const date = this.selectedDate;
      const [year, month] = date.split("-").map(Number);
      const lastDay = new Date(year, month, 0).getDate();
      return lastDay;
    },
    totalUrinesTimes() {
      return this.calculateTotal(this.urinesTimes);
    },
    averageUrinesTimes() {
      return this.calculateAverage(this.urinesTimes);
    },
    morningPercentage() {
      const total = this.totalUrinesTimes;
      return total > 0 ? Math.round((this.urinesTimes[0] / total) * 100) : 0;
    },
    lunchPercentage() {
      const total = this.totalUrinesTimes;
      return total > 0 ? Math.round((this.urinesTimes[1] / total) * 100) : 0;
    },
    eveningPercentage() {
      const total = this.totalUrinesTimes;
      return total > 0 ? Math.round((this.urinesTimes[2] / total) * 100) : 0;
    },
  },
  mounted() {
    this.drawChart();
  },
  watch: {
    urinesTimes: {
      handler() {
        this.$nextTick(() => {
          this.drawChart();
        });
      },
      deep: true,
    },
  },
  methods: {
    calculateTotal(array, defaultValue = 0) {
      if (!Array.isArray(array) || array.length === 0) {
        return defaultValue;
      }
      return array.reduce((acc, cur) => {
        const num = Number(cur);
        return acc + (isNaN(num) ? 0 : num);
      }, 0);
    },
    calculateAverage(array, defaultValue = 0) {
      if (!Array.isArray(array) || array.length === 0) {
        return defaultValue;
      }
      const total = this.calculateTotal(array);
      return Math.round(total / array.length);
    },
    getCircleRadius(percentage) {
      if (percentage === 0) {
        return 15; // 0%일 때 최소 크기
      }
      // 퍼센테지에 비례하여 반지름 조정
      const minRadius = 20;
      const maxRadius = 45;
      return (
        minRadius + Math.round((percentage / 100) * (maxRadius - minRadius))
      );
    },

    calculateCirclePositions() {
      // 각 원의 반지름 계산
      const morningRadius = this.getCircleRadius(this.morningPercentage);
      const lunchRadius = this.getCircleRadius(this.lunchPercentage);
      const eveningRadius = this.getCircleRadius(this.eveningPercentage);

      // 원들을 크기 순으로 정렬하여 가장 큰 원을 중앙에 배치
      const circles = [
        {
          type: "morning",
          radius: morningRadius,
          percentage: this.morningPercentage,
        },
        {
          type: "lunch",
          radius: lunchRadius,
          percentage: this.lunchPercentage,
        },
        {
          type: "evening",
          radius: eveningRadius,
          percentage: this.eveningPercentage,
        },
      ].sort((a, b) => b.radius - a.radius);

      const tempPositions = {};

      // 모든 원이 같은 크기인지 확인
      const allSameSize =
        circles[0].radius === circles[1].radius &&
        circles[1].radius === circles[2].radius;

      if (allSameSize) {
        // 모든 원이 같은 크기일 때 삼각형 배치 (딱 닿게)
        const radius = circles[0].radius;
        const distance = radius * 2; // 반지름 * 2 = 지름 (딱 닿게)

        tempPositions[circles[0].type] = {
          x: 0,
          y: -distance * 0.577, // 정삼각형 높이 비율
        };
        tempPositions[circles[1].type] = {
          x: -distance * 0.5,
          y: distance * 0.289,
        };
        tempPositions[circles[2].type] = {
          x: distance * 0.5,
          y: distance * 0.289,
        };
      } else {
        // 크기가 다를 때 - 가장 큰 원을 중앙에 배치하고 나머지는 딱 닿게
        tempPositions[circles[0].type] = { x: 0, y: 0 };

        // 두 번째 원을 첫 번째 원에 딱 닿게 배치
        const distance1 = circles[0].radius + circles[1].radius;
        const angle1 = Math.PI / 6; // 30도로 조정하여 더 자연스럽게
        tempPositions[circles[1].type] = {
          x: Math.cos(angle1) * distance1,
          y: Math.sin(angle1) * distance1,
        };

        // 세 번째 원을 첫 번째 원에 딱 닿게 배치 (반대편)
        const distance2 = circles[0].radius + circles[2].radius;
        const angle2 = -Math.PI / 6; // -30도
        tempPositions[circles[2].type] = {
          x: Math.cos(angle2) * distance2,
          y: Math.sin(angle2) * distance2,
        };
      }

      // 전체 그룹의 경계 계산
      const allPositions = Object.values(tempPositions);
      const allTypes = Object.keys(tempPositions);

      let minX = Infinity,
        maxX = -Infinity,
        minY = Infinity,
        maxY = -Infinity;

      allPositions.forEach((pos, index) => {
        const type = allTypes[index];
        const radius =
          type === "morning"
            ? morningRadius
            : type === "lunch"
            ? lunchRadius
            : eveningRadius;

        minX = Math.min(minX, pos.x - radius);
        maxX = Math.max(maxX, pos.x + radius);
        minY = Math.min(minY, pos.y - radius);
        maxY = Math.max(maxY, pos.y + radius);
      });

      // 전체 그룹의 중심점 계산
      const groupCenterX = (minX + maxX) / 2;
      const groupCenterY = (minY + maxY) / 2;

      // 캔버스 중심점
      const canvasCenterX = this.canvasWidth / 2;
      const canvasCenterY = this.canvasHeight / 2;

      // 오프셋 계산하여 그룹을 캔버스 중앙으로 이동
      const offsetX = canvasCenterX - groupCenterX;
      const offsetY = canvasCenterY - groupCenterY;

      // 최종 위치 계산
      const positions = {};
      Object.keys(tempPositions).forEach((type) => {
        positions[type] = {
          x: tempPositions[type].x + offsetX,
          y: tempPositions[type].y + offsetY,
        };
      });

      return positions;
    },
    drawChart() {
      const canvas = this.$refs.chartCanvas;
      if (!canvas) return;

      const ctx = canvas.getContext("2d");

      // Canvas 초기화
      ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);

      // 동적으로 계산된 위치 가져오기
      const positions = this.calculateCirclePositions();

      // 원 데이터 설정 - 퍼센테이지에 따라 동적으로 위치 조정
      const circles = [
        {
          x: positions.morning.x,
          y: positions.morning.y,
          radius: this.getCircleRadius(this.morningPercentage),
          color: "#c9f4f8",
          percentage: this.morningPercentage,
          label: "아침",
        },
        {
          x: positions.evening.x,
          y: positions.evening.y,
          radius: this.getCircleRadius(this.eveningPercentage),
          color: "#a7a7a7",
          percentage: this.eveningPercentage,
          label: "저녁",
        },
        {
          x: positions.lunch.x,
          y: positions.lunch.y,
          radius: this.getCircleRadius(this.lunchPercentage),
          color: "#ffcc00",
          percentage: this.lunchPercentage,
          label: "점심",
        },
      ];

      // 원 그리기 (큰 원부터 뒤에서)
      const sortedCircles = circles.sort((a, b) => b.radius - a.radius);

      sortedCircles.forEach((circle) => {
        ctx.beginPath();
        ctx.arc(circle.x, circle.y, circle.radius, 0, 2 * Math.PI);
        ctx.fillStyle = circle.color;
        ctx.fill();

        // 퍼센테지 텍스트
        ctx.fillStyle = "#333333";
        ctx.font = "bold 13px Arial";
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";
        ctx.fillText(`${circle.percentage}%`, circle.x, circle.y);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.urine-time-section {
  width: 100% !important;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-content: flex-start;
}

.urine-time-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.urine-time-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.urine-time-text {
  font-weight: 500;
  font-size: 14px;
  color: black;
  text-align: left;
  margin: 0;
}

.chart-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 0;
}

.bubble-chart {
  max-width: 100%;
  height: auto;
}

.legend {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #666;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;

  &.morning-color {
    background-color: #c9f4f8;
  }

  &.lunch-color {
    background-color: #ffcc00;
  }

  &.evening-color {
    background-color: #a7a7a7;
  }
}

.urine-time-footer {
  width: 100%;
  background: #f8f8f8;
  padding: 25px 20px;
  border-radius: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.point-color {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
}

.urine-time-item-title {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: black;
  font-weight: 500;
  margin-bottom: 10px;
}

.urine-time-item {
  display: flex;
  flex-direction: column;
  justify-self: start;

  > .item-time {
    font-weight: bold;
    font-size: 14px;
    color: black;
    letter-spacing: -3%;
    margin-bottom: 10px;
    text-align: center;
  }

  > .hours-range {
    font-family: GilroyMedium !important;
    font-size: 12px;
    color: #000;
    font-weight: 500;
  }
}
</style>
