<template>
  <main class="water-section">
    <!-- 수분 섭취량 헤더 -->
    <header class="water-header">
      <h3>수분 섭취량</h3>
      <span>총 {{ totalDaysRecord }}일 기록</span>
    </header>
    <section>
      <!-- 수분 섭취량 요약글 -->
      <article>
        <p class="water-summary">
          한달 평균 하루 수분 섭취량은 {{ averageWaterRecord }} 입니다. 이번달
          전체 수분 섭취량은 총 {{ totalWaterRecord }} 입니다.
        </p>
      </article>

      <!-- 수분 섭취량 글라스 이미지-->
      <article class="glass-container">
        <div class="glass-mask-container">
          <img
            src="@/assets/images_assets/images/glass_mask.png"
            alt="glass_mask"
          />
        </div>
        <div class="circle">
          <div class="wave" ref="water"></div>
          <div class="value-txt" ref="valueTxt">{{ totalWaterRecord }}</div>
        </div>
      </article>
    </section>
    <footer class="water-guide">
      <span class="water-guide-text">{{ checkWaterText }}</span>
    </footer>
  </main>
</template>

<script>
import { formatWaterVolume } from "./utils/water.utils";

export default {
  name: "ReportWater",

  props: {
    selectedDate: {
      type: String,
      required: true,
      default: () => {
        const date = new Date();

        return `${date.getFullYear()}-${date.getMonth() + 1}`;
      },
    },

    targetWater: {
      type: Number,
      required: true,
      default: 0,
    },

    averageWater: {
      type: Number,
      required: true,
      default: 0,
    },

    totalWater: {
      type: Number,
      required: true,
      default: 0,
    },
  },

  data() {
    return {
      topPosition: 255,
    };
  },

  computed: {
    totalDaysRecord() {
      const date = this.selectedDate;
      const [year, month] = date.split("-").map(Number);
      const lastDay = new Date(year, month, 0).getDate();

      return lastDay;
    },

    totalWaterRecord() {
      return formatWaterVolume(this.totalWater);
    },

    averageWaterRecord() {
      return formatWaterVolume(this.averageWater);
    },

    checkWaterText() {
      let text = "";

      if (!this.averageWater) {
        return "수분 기록이 없어요. 지금 한 잔 마시고 기록해볼까요? 💧";
      }

      if (this.targetWater <= this.currentWater) {
        text = "목표를 달성했습니다! 👏🏻​";
      } else {
        const waterDiff = this.targetWater - this.averageWater;
        const absWaterDiff = Math.abs(waterDiff);

        if (waterDiff < 0) {
          text = "목표를 달성했습니다! 👏🏻​";
        } else {
          text = `목표까지 섭취량을 ${formatWaterVolume(
            absWaterDiff
          )} 늘려야합니다.`;
        }
      }

      return `나의 목표 하루 수분 섭취량은 ${formatWaterVolume(
        this.targetWater
      )} 입니다. ${text}`;
    },
  },

  watch: {
    totalWater: {
      handler(_newValue) {
        this.setWaterLevel();
      },
      immediate: true, // 컴포넌트 마운트 시에도 실행
    },
  },

  mounted() {
    // watch에서 immediate: true로 처리하므로 중복 호출 방지
  },

  methods: {
    // totalWater 값에 따라 물 레벨 설정
    setWaterLevel() {
      if (!this.$refs.water || !this.$refs.valueTxt) {
        // refs가 아직 준비되지 않은 경우 다음 틱에 재시도
        this.$nextTick(() => {
          this.setWaterLevel();
        });
        return;
      }

      const waterValue = this.totalWater;
      this.topPosition = this.computedTopPosition(waterValue);

      this.$refs.water.style.top = `${this.topPosition}px`;

      // 물 레벨이 높을 때 텍스트 색상 변경
      if (waterValue >= 1100) {
        this.$refs.valueTxt.style.color = "#ffffff";
      } else {
        this.$refs.valueTxt.style.color = "#41d8e6";
      }
    },

    computedTopPosition(val) {
      const increaseValue = 206 / 20;
      const currentVolume = val > 2000 ? 2000 / 100 : val / 100;
      const calculateVolume = currentVolume * increaseValue;
      const topPosition = 206 - (calculateVolume + 12.5);

      return topPosition;
    },
  },
};
</script>

<style lang="scss" scoped>
.water-section {
  width: 100% !important;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-content: flex-start;
}

.water-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.water-summary {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  text-align: start;
  letter-spacing: -3%;
}

.glass-mask-container {
  width: 164px;
  display: flex;
  justify-content: center;
  overflow: hidden;
  z-index: 1;

  > img {
    width: 100%;
    height: 186px;
  }
}

.glass-container {
  width: 100%;
  display: flex;
  justify-content: center;
}

.circle {
  background-color: #f8f8f8;
  position: absolute;
  width: 164px;
  height: 186px;
  left: 50%;
  transform: translateX(-50%);
  overflow: hidden;
  transition: all 1s;
}

.wave {
  position: absolute;
  width: 200px;
  height: 200px;
  border-radius: 43%;
  left: 50%;
  transform: translateX(-10%);
  background: #41d8e6;
  animation: wave 6s infinite linear;
  transition: all 1s;
  opacity: 0.7;
}

.value-txt {
  position: absolute;
  font-family: GilroyBold !important;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.4s;
  font-size: 26px;
  color: #41d8e6;
  z-index: 99;
}

@keyframes wave {
  0% {
    transform: translate(-50%) rotate(-90deg);
  }

  100% {
    transform: translate(-50%) rotate(360deg);
  }
}

.water-guide {
  width: 100%;
  background: #f8f8f8;
  padding: 30px 20px;
  border-radius: 10px;
}

.water-guide-text {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  line-height: 25px;
  letter-spacing: -3%;
}
</style>
