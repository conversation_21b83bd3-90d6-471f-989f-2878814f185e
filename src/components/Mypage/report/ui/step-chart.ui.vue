<template>
  <div class="chart-container">
    <apexChart
      type="bar"
      height="200"
      :options="chartOptions"
      :series="series"
    />
  </div>
</template>

<script>
import Vue from "vue";
import VueApexCharts from "vue-apexcharts";

// 전역으로 컴포넌트 등록
Vue.use(VueApexCharts);
Vue.component("apexChart", VueApexCharts);

export default {
  name: "BarChart",
  props: {
    // formatForChart 함수의 반환 타입과 동일한 데이터를 받습니다.
    // 예시: [{ week: '1주', value: 59102, label: '59,102' }, ...]
    chartData: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  data() {
    return {
      // data 속성은 비워두고 computed 속성에서 동적으로 처리합니다.
    };
  },
  computed: {
    series() {
      return [
        {
          name: "값",
          data: this.chartData.map((item) => item.value), // 실제 숫자 값만 전달
        },
      ];
    },
    chartOptions() {
      return {
        chart: {
          type: "bar",
          height: 200,
          toolbar: {
            show: false,
          },
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: "25%", // 막대 너비를 더 줄여 알약 형태를 강조
            borderRadius: 12, // 막대 모서리 둥글기 (columnWidth에 맞춰 조정)
            endingShape: "rounded", // 상단 둥글게
          },
        },
        dataLabels: {
          enabled: true,
          offsetY: 110, // **이 값을 -18로 조정하여 시각적으로 막대보다 약 10px 위에 위치하도록 함**
          style: {
            fontSize: "14px",
            colors: ["#333"],
            fontWeight: 600, // 글씨 두께
          },
          formatter: function (val) {
            return val.toLocaleString(); // 받은 숫자 값을 직접 포맷팅하여 표시
          },
        },
        stroke: {
          show: true,
          width: 2,
          colors: ["transparent"],
        },
        xaxis: {
          categories: this.chartData.map((item) => item.week),
          labels: {
            style: {
              fontSize: "14px",
              colors: "#333",
              fontWeight: 600, // 글씨 두께
            },
          },
          axisBorder: {
            show: false,
          },
          axisTicks: {
            show: false,
          },
        },
        yaxis: {
          show: false, // Y축 라벨 숨기기
          // Y축 최소/최대 값을 명시적으로 설정하여 라벨 위치의 일관성을 높일 수 있습니다.
          // min: 0,
          // max: Math.max(...this.chartData.map(item => item.value)) * 1.2 // 최대값의 120%
        },
        fill: {
          opacity: 1,
          colors: ["#41D8E6"], // 막대 색상 (이미지와 유사한 청록색)
        },
        tooltip: {
          enabled: false, // 툴팁 비활성화
        },
        grid: {
          show: false, // 그리드 라인 숨기기
        },
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.chart-container {
  padding: 1rem;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  max-height: 200px;

  $primary-chart-color: #41d8e6;
  $text-color: #000000;

  h2 {
    color: $text-color;
    text-align: center;
    margin-bottom: 1rem;
  }

  .apexcharts-canvas {
    margin: 0 auto;
  }
}
</style>
